ALXTimeLogger Application Documentation
Overview
The ALXTimeLogger is a Salesforce application designed to track time spent on various Salesforce objects. This document provides a comprehensive overview of the application's components, functionality, and implementation details.

Application Components
Custom Objects
Time_Log__c
The core object that stores time tracking information with the following key fields:

Name: Auto-number field (TL-{00000})
Object_Type__c: Picklist field to categorize the type of object being tracked (Account, Case, Opportunity, Potential, etc.)
Related_Object__c: Text field that stores the ID of the related record
Start_Time__c: DateTime field for when the time tracking started
End_Time__c: DateTime field for when the time tracking ended
Duration_Hours__c: Formula field that calculates the time difference
Description__c: Text area for notes about the time log
Billable__c: Checkbox to indicate if the time is billable
User__c: Lookup to the User who created the time log
The Time_Log__c object also includes specific lookup relationships to standard and custom objects:

Account__c
Case__c
Contact__c
Opportunity__c
Potential__c (Custom object)
Lightning Web Components
1. timeLogForm
Purpose: Allows users to create time logs directly from record pages.

Files:

force-app/main/default/lwc/timeLogForm/timeLogForm.html - Component markup
force-app/main/default/lwc/timeLogForm/timeLogForm.js - JavaScript controller
force-app/main/default/lwc/timeLogForm/timeLogForm.js-meta.xml - Component configuration
Key Features:

Automatically detects the current record context (object type and record ID)
Provides a form to enter time log details (start time, end time, description, billable status)
Creates Time_Log__c records with proper relationships to the source object
Supports multiple object types including standard objects (Account, Case, Contact, Opportunity) and custom objects (Potential__c, Assessment__c, Campaign, Case_Activity__c, mone__Mailchimp_Campaign__c, inspire1__Project__c, Recognition__c)
Object Type Mapping:
The component maps Salesforce API names to user-friendly labels:

const objectTypeMapping = {
    'Case': 'Case',
    'Account': 'Account',
    'Contact': 'Contact',
    'Opportunity': 'Opportunity',
    'Potential__c': 'Potential',
    'Assessment__c': 'Assessment',
    'Campaign': 'Campaign',
    'Case_Activity__c': 'Case Activity',
    'mone__Mailchimp_Campaign__c': 'Custom 
Lookup Field Mapping:
Maps object types to their corresponding lookup fields in the Time_Log__c object:

2. timeLogSummary
Purpose: Displays a summary of recent time logs.

Files:

force-app/main/default/lwc/timeLogSummary/timeLogSummary.html
force-app/main/default/lwc/timeLogSummary/timeLogSummary.js
force-app/main/default/lwc/timeLogSummary/timeLogSummary.js-meta.xml
Key Features:

Configurable number of records to display
Can be added to home pages, app pages, or record pages
Shows recent time logs with key information
3. timeLogStats
Purpose: Displays statistics and charts for time logs.

Files:

force-app/main/default/lwc/timeLogStats/timeLogStats.html
force-app/main/default/lwc/timeLogStats/timeLogStats.js
force-app/main/default/lwc/timeLogStats/timeLogStats.js-meta.xml
Key Features:

Visual representation of time log data
Can be configured for different objects including Case, mone__Mailchimp_Campaign__c, and inspire1__Project__c
4. employeeTimeLogDashboard
Purpose: Comprehensive dashboard showing time logs by employee with detailed views.

Files:

force-app/main/default/lwc/employeeTimeLogDashboard/employeeTimeLogDashboard.html
force-app/main/default/lwc/employeeTimeLogDashboard/employeeTimeLogDashboard.js
force-app/main/default/lwc/employeeTimeLogDashboard/employeeTimeLogDashboard.js-meta.xml
Key Features:

Two-panel layout with employees listed on the left side
Time log details for the selected employee on the right side
Summary statistics and filterable time logs
Time period filtering options (Current Week, Last Week, This Month, etc.)
Bar chart visualization for the selected time period
Filters out system-generated Salesforce users
Apex Classes
TimeLoggerController
Purpose: Apex controller that provides backend functionality for the Lightning components.

Key Methods:

Methods to query time logs
Methods to create and update time logs
Methods to retrieve user information
TimeLoggerService
Purpose: Service class that contains business logic for time logging.

Key Features:

Validation rules for time logs
Calculation methods for time durations
Business logic for handling different object types
Reports and Dashboards
Time Log Reports
Time by Object Type: Shows time spent on different object types
Billable vs. NonBillable Time: Compares billable and non-billable time
Time Logs by User: Shows time logs grouped by user
Time Tracking Dashboard
A comprehensive dashboard that includes:

Time by Object Type (donut chart)
Billable vs. Non-Billable Time (donut chart)
Additional visualizations of time log data
Custom Tabs
Time_Log__c: Tab for accessing the Time Log object
Employee_Time_Log_Dashboard: Tab for accessing the employee time log dashboard
Permission Sets
Time_Log_User
Purpose: Provides access to view and create Time Logs.

Permissions:

Create, Read, Edit, Delete permissions on Time_Log__c
Access to related fields and objects
Implementation Details
Object Detection
The timeLogForm component automatically detects the current record context using the recordId and objectApiName properties provided by the Lightning framework. It maps the API name to a user-friendly label using the objectTypeMapping object.

Time Log Creation
When a user submits the form, the component:

Validates the input data
Creates a Time_Log__c record with the appropriate field values
Sets the specific lookup field based on the object type
Associates the time log with the current user
Error Handling
The component includes comprehensive error handling:

Field accessibility checks
Validation of input data
Specific error messages for different types of errors
Graceful degradation when fields are not accessible
Recent Enhancements
Support for Potential__c Object
The timeLogForm component has been enhanced to support the Potential__c custom object:

Added Potential__c to the list of supported objects in the component's meta XML
Added 'Potential__c': 'Potential' mapping in the objectTypeMapping
Added 'Potential': 'Potential__c' to the lookupFieldMapping
Added 'Potential' to the objectTypeOptions dropdown
Support for Additional Custom Objects
The component now supports several additional custom objects:

Assessment__c
Campaign
Case_Activity__c
Recognition__c
mone__Mailchimp_Campaign__c
inspire1__Project__c
Usage Instructions
Adding the Time Log Form to a Record Page
Navigate to the record page you want to modify in the Lightning App Builder
Search for "Time Log Form" in the Components panel
Drag and drop the component onto the page layout
Save and activate the page
Creating a Time Log
Navigate to a record page where the Time Log Form is displayed
The form will automatically detect the object type and record ID
Enter the start time, end time, and description
Check the "Billable" checkbox if applicable
Click "Create Time Log"
Viewing Time Log Statistics
Add the Time Log Stats component to a record page or app page
Configure the component properties as needed
View the statistics and charts for time logs
Using the Employee Time Log Dashboard
Navigate to the Employee Time Log Dashboard tab
Select a time period filter (Current Week, Last Week, etc.)
Select an employee from the list on the left
View the detailed time log information and statistics on the right
Deployment Instructions
To deploy the ALXTimeLogger application to a Salesforce org:

Use the Salesforce CLI with the following command:
Assign the Time_Log_User permission set to users who need access:
Conclusion
The ALXTimeLogger application provides a comprehensive solution for tracking time spent on various Salesforce objects. With its intuitive interface and powerful features, it enables organizations to better understand how time is allocated across different activities and objects.

25 files changed
+1230 -187
