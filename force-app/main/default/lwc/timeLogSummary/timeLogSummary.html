<template>
    <lightning-card title={title} icon-name="standard:timesheet_entry">
        <div slot="actions">
            <template if:true={showRefreshButton}>
                <lightning-button-icon 
                    icon-name="utility:refresh" 
                    alternative-text="Refresh" 
                    title="Refresh" 
                    onclick={handleRefresh}>
                </lightning-button-icon>
            </template>
        </div>
        
        <div class="slds-p-horizontal_small">
            <template if:true={isLoading}>
                <lightning-spinner alternative-text="Loading" size="small"></lightning-spinner>
            </template>
            
            <template if:true={error}>
                <div class="slds-text-color_error slds-p-around_small">
                    Error loading time logs: {error.message}
                </div>
            </template>
            
            <template if:false={isLoading}>
                <template if:true={timeLogs.length}>
                    <ul class="slds-has-dividers_bottom-space">
                        <template for:each={timeLogs} for:item="log">
                            <li key={log.Id} class="slds-item slds-p-vertical_x-small">
                                <div class="slds-grid slds-gutters">
                                    <div class="slds-col slds-size_1-of-12">
                                        <lightning-icon icon-name={log.objectTypeIcon} size="small"></lightning-icon>
                                    </div>
                                    <div class="slds-col slds-size_11-of-12">
                                        <div class="slds-grid slds-wrap">
                                            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                                                <a onclick={navigateToRecord} data-id={log.Id} class="slds-text-link_reset">
                                                    <div class="slds-text-heading_small">{log.Description__c}</div>
                                                </a>
                                            </div>
                                            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2 slds-text-align_right">
                                                <template if:true={log.Duration_Hours__c}>
                                                    <span class="slds-badge slds-badge_lightest">
                                                        {log.Duration_Hours__c} hours
                                                    </span>
                                                </template>
                                                <template if:true={log.Billable__c}>
                                                    <span class="slds-badge slds-badge_success slds-m-left_x-small">
                                                        Billable
                                                    </span>
                                                </template>
                                            </div>
                                            <div class="slds-col slds-size_1-of-1 slds-text-body_small slds-text-color_weak">
                                                <div class="slds-grid slds-gutters">
                                                    <div class="slds-col">
                                                        <lightning-formatted-text value={log.Object_Type__c}></lightning-formatted-text>
                                                        <template if:true={log.Related_Object__c}>
                                                            : {log.Related_Object__c}
                                                        </template>
                                                    </div>
                                                    <div class="slds-col slds-text-align_right">
                                                        {log.formattedStartTime}
                                                        <template if:true={log.formattedEndTime}>
                                                            - {log.formattedEndTime}
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </template>
                    </ul>
                </template>
                <template if:false={timeLogs.length}>
                    <div class="slds-text-align_center slds-p-around_medium slds-text-color_weak">
                        No time logs found.
                    </div>
                </template>
            </template>
        </div>
        
        <div slot="footer">
            <lightning-button 
                label="View All Time Logs" 
                variant="base" 
                onclick={navigateToTimeLogTab} 
                class="slds-m-left_x-small">
            </lightning-button>
        </div>
    </lightning-card>
</template>
