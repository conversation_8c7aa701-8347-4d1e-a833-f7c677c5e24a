<template>
    <lightning-card title="Create Time Log" icon-name="standard:timesheet_entry">
        <div class="slds-p-around_medium">
            <!-- Loading spinner while checking field accessibility -->
            <template if:true={isLoading}>
                <div class="slds-is-relative slds-m-bottom_medium">
                    <lightning-spinner alternative-text="Loading" size="small"></lightning-spinner>
                    <p class="slds-text-align_center slds-m-top_x-small">Checking field accessibility...</p>
                </div>
            </template>

            <!-- Field Access Error Alert -->
            <template if:true={fieldAccessError}>
                <div class="slds-notify slds-notify_alert slds-theme_error" role="alert">
                    <span class="slds-assistive-text">Error</span>
                    <lightning-icon icon-name="utility:error" alternative-text="Error" size="small" class="slds-m-right_x-small"></lightning-icon>
                    <h2>
                        Field Access Error: The Time Log object or its fields are not accessible to your user profile.
                        Please contact your administrator to update field-level security settings for the Time_Log__c object,
                        especially the Object_Type__c field.
                    </h2>
                </div>
                <div class="slds-box slds-theme_info slds-m-top_small">
                    <p>
                        <strong>Troubleshooting Steps:</strong>
                    </p>
                    <ol class="slds-list_ordered">
                        <li>Go to Setup > Object Manager > Time Log > Fields & Relationships > Object Type</li>
                        <li>Click "Set Field-Level Security"</li>
                        <li>Ensure your profile has "Visible" checked</li>
                        <li>Save the changes and refresh this page</li>
                    </ol>
                </div>
            </template>

            <!-- Object Type field - show differently based on context -->
            <template if:true={recordId}>
                <!-- When on a record page, show as read-only field with pre-filled value -->
                <div class="slds-form-element">
                    <label class="slds-form-element__label">
                        <abbr class="slds-required" title="required">*</abbr>Object Type
                    </label>
                    <div class="slds-form-element__control">
                        <div class="slds-form-element__static">{objectType}</div>
                    </div>
                </div>
            </template>

            <template if:false={recordId}>
                <!-- When not on a record page, show as dropdown -->
                <lightning-combobox
                    label="Object Type"
                    name="objectType"
                    value={objectType}
                    options={objectTypeOptions}
                    onchange={handleObjectTypeChange}
                    required
                ></lightning-combobox>
            </template>

            <!-- Related Record ID field - show differently based on context -->
            <template if:true={recordId}>
                <!-- When on a record page, show as read-only field with pre-filled value -->
                <div class="slds-m-top_small">
                    <div class="slds-form-element">
                        <label class="slds-form-element__label">Related Record ID</label>
                        <div class="slds-form-element__control">
                            <div class="slds-form-element__static">{relatedObject}</div>
                        </div>
                    </div>
                </div>
            </template>

            <template if:false={recordId}>
                <!-- When not on a record page, show as input field -->
                <lightning-input
                    type="text"
                    label="Related Record ID"
                    value={relatedObject}
                    onchange={handleRelatedObjectChange}
                    class="slds-m-top_small"
                ></lightning-input>
            </template>

            <lightning-input
                type="datetime"
                label="Start Time"
                value={startTime}
                onchange={handleStartTimeChange}
                class="slds-m-top_small"
                required
            ></lightning-input>

            <lightning-input
                type="datetime"
                label="End Time"
                value={endTime}
                onchange={handleEndTimeChange}
                class="slds-m-top_small"
                required
            ></lightning-input>

            <lightning-textarea
                label="Description"
                value={description}
                onchange={handleDescriptionChange}
                class="slds-m-top_small"
            ></lightning-textarea>

            <lightning-input
                type="checkbox"
                label="Billable"
                checked={billable}
                onchange={handleBillableChange}
                class="slds-m-top_small"
            ></lightning-input>

            <lightning-button
                label="Save"
                variant="brand"
                onclick={handleSubmit}
                class="slds-m-top_medium"
                disabled={isButtonDisabled}
            ></lightning-button>
        </div>
    </lightning-card>
</template>
